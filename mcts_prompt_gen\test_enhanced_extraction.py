#!/usr/bin/env python3
"""
Test script for enhanced prompt extraction functionality.

This script tests the new enhanced prompt extraction capabilities that include
ComfyUI workflow parameters like CFG, steps, dimensions, LoRAs, etc.
"""

import os
import sys
import tempfile
from pathlib import Path
from PIL import Image
from PIL.PngImagePlugin import PngInfo
import json

# Add src directory to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)  # Insert at beginning to prioritize our utils
from utils import image_info, extract_comfyui_workflow_params, format_enhanced_prompt


def create_test_image_with_comfyui_metadata(path: str, workflow_data: dict, basic_prompt: str = "test prompt"):
    """Create a test image with ComfyUI workflow metadata."""
    # Create a simple test image
    img = Image.new('RGB', (512, 512), color='blue')
    
    # Create PNG metadata
    pnginfo = PngInfo()
    
    # Add basic prompt
    pnginfo.add_text("parameters", basic_prompt)
    
    # Add ComfyUI workflow as JSON
    workflow_json = json.dumps(workflow_data)
    pnginfo.add_text("workflow", workflow_json)
    
    # Save with metadata
    img.save(path, pnginfo=pnginfo)
    img.close()


def create_sample_comfyui_workflow():
    """Create a sample ComfyUI workflow for testing."""
    return {
        "4": {
            "inputs": {
                "ckpt_name": "animagineXLV3_v30.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {
                "title": "Load Checkpoint"
            }
        },
        "6": {
            "inputs": {
                "text": "1girl, masterpiece, best quality, detailed face, beautiful eyes",
                "clip": ["41", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {
                "title": "CLIP Text Encode (Prompt)"
            }
        },
        "7": {
            "inputs": {
                "text": "embedding:DeepNegative_xl_v1, low quality, blurry",
                "clip": ["41", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {
                "title": "CLIP Text Encode (Negative Prompt)"
            }
        },
        "12": {
            "inputs": {
                "seed": 123456789,
                "steps": 35,
                "cfg": 7.5,
                "sampler_name": "euler_ancestral",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["41", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["30", 0]
            },
            "class_type": "KSampler",
            "_meta": {
                "title": "KSampler"
            }
        },
        "30": {
            "inputs": {
                "width": 832,
                "height": 1216,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {
                "title": "Empty Latent Image"
            }
        },
        "41": {
            "inputs": {
                "text": "<lora:character_lora.safetensors:1.0>, <lora:style\\watercolor.safetensors:0.8>",
                "model": ["4", 0],
                "clip": ["4", 1]
            },
            "class_type": "LoraTagLoader",
            "_meta": {
                "title": "Load LoRA Tag"
            }
        }
    }


def test_workflow_parameter_extraction():
    """Test the workflow parameter extraction function."""
    print("Testing workflow parameter extraction...")
    
    workflow = create_sample_comfyui_workflow()
    workflow_json = json.dumps(workflow)
    
    params = extract_comfyui_workflow_params(workflow_json)
    
    print("Extracted parameters:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # Verify expected parameters
    expected_params = {
        'cfg': 7.5,
        'steps': 35,
        'width': 832,
        'height': 1216,
        'model': 'animagineXLV3_v30.safetensors',
        'sampler_name': 'euler_ancestral',
        'scheduler': 'normal'
    }
    
    success = True
    for key, expected_value in expected_params.items():
        if key not in params:
            print(f"❌ Missing parameter: {key}")
            success = False
        elif params[key] != expected_value:
            print(f"❌ Parameter mismatch for {key}: expected {expected_value}, got {params[key]}")
            success = False
        else:
            print(f"✓ Parameter {key}: {params[key]}")
    
    # Check LoRAs
    if 'loras' in params:
        print(f"✓ LoRAs extracted: {params['loras']}")
        expected_loras = ['character_lora.safetensors:1.0', 'watercolor.safetensors:0.8']
        if len(params['loras']) == len(expected_loras):
            print("✓ LoRA count matches expected")
        else:
            print(f"❌ LoRA count mismatch: expected {len(expected_loras)}, got {len(params['loras'])}")
            success = False
    else:
        print("❌ No LoRAs extracted")
        success = False
    
    return success


def test_enhanced_prompt_formatting():
    """Test the enhanced prompt formatting function."""
    print("\nTesting enhanced prompt formatting...")
    
    base_prompt = "1girl, masterpiece, best quality"
    params = {
        'cfg': 7.5,
        'steps': 35,
        'width': 832,
        'height': 1216,
        'sampler_name': 'euler_ancestral',
        'model': 'animagineXLV3_v30.safetensors',
        'loras': ['character_lora.safetensors:1.0', 'watercolor.safetensors:0.8']
    }
    
    enhanced_prompt = format_enhanced_prompt(base_prompt, params)
    print(f"Base prompt: {base_prompt}")
    print(f"Enhanced prompt: {enhanced_prompt}")
    
    # Check that all parameters are included
    expected_parts = [
        "--cfg 7.5",
        "--steps 35",
        "--size 832x1216",
        "--sampler euler_ancestral",
        "--model animagineXLV3_v30",
        "--lora character_lora.safetensors:1.0",
        "--lora watercolor.safetensors:0.8"
    ]
    
    success = True
    for part in expected_parts:
        if part not in enhanced_prompt:
            print(f"❌ Missing in enhanced prompt: {part}")
            success = False
        else:
            print(f"✓ Found in enhanced prompt: {part}")
    
    return success


def test_image_info_enhanced_extraction():
    """Test the enhanced image_info function with a real image."""
    print("\nTesting enhanced image_info extraction...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_image_path = Path(temp_dir) / "test_enhanced.png"
        
        # Create test image with ComfyUI metadata
        workflow = create_sample_comfyui_workflow()
        create_test_image_with_comfyui_metadata(
            str(test_image_path), 
            workflow, 
            "basic prompt from parameters"
        )
        
        # Test enhanced extraction
        with Image.open(test_image_path) as img:
            # Test with enhanced prompts enabled
            info_enhanced = image_info(img, str(test_image_path), enhanced_prompts=True)
            
            # Test with enhanced prompts disabled
            info_basic = image_info(img, str(test_image_path), enhanced_prompts=False)
        
        print("Basic extraction result:")
        print(f"  Prompt: {info_basic.get('prompt', 'N/A')}")
        
        print("Enhanced extraction result:")
        print(f"  Prompt: {info_enhanced.get('prompt', 'N/A')}")
        print(f"  Original prompt: {info_enhanced.get('original_prompt', 'N/A')}")
        
        if 'workflow_params' in info_enhanced:
            print("  Workflow parameters found:")
            for key, value in info_enhanced['workflow_params'].items():
                print(f"    {key}: {value}")
        else:
            print("  No workflow parameters found")
        
        # Check if enhanced prompt contains parameter information
        enhanced_prompt = info_enhanced.get('prompt', '')
        success = True
        
        if '--cfg' not in enhanced_prompt:
            print("❌ Enhanced prompt missing CFG parameter")
            success = False
        else:
            print("✓ Enhanced prompt contains CFG parameter")
        
        if '--steps' not in enhanced_prompt:
            print("❌ Enhanced prompt missing steps parameter")
            success = False
        else:
            print("✓ Enhanced prompt contains steps parameter")
        
        if '--lora' not in enhanced_prompt:
            print("❌ Enhanced prompt missing LoRA parameters")
            success = False
        else:
            print("✓ Enhanced prompt contains LoRA parameters")
        
        return success


def test_backward_compatibility():
    """Test that the enhanced extraction maintains backward compatibility."""
    print("\nTesting backward compatibility...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_image_path = Path(temp_dir) / "test_basic.png"
        
        # Create test image with only basic metadata (no ComfyUI workflow)
        img = Image.new('RGB', (256, 256), color='red')
        pnginfo = PngInfo()
        pnginfo.add_text("parameters", "simple test prompt, high quality")
        img.save(str(test_image_path), pnginfo=pnginfo)
        img.close()
        
        # Test extraction
        with Image.open(test_image_path) as img:
            info = image_info(img, str(test_image_path), enhanced_prompts=True)
        
        prompt = info.get('prompt', '')
        original_prompt = info.get('original_prompt', '')
        
        print(f"Extracted prompt: {prompt}")
        print(f"Original prompt: {original_prompt}")
        
        # For basic images, prompt and original_prompt should be the same
        if prompt == original_prompt and prompt == "simple test prompt, high quality":
            print("✓ Backward compatibility maintained")
            return True
        else:
            print("❌ Backward compatibility broken")
            return False


def main():
    """Run all tests."""
    print("Enhanced Prompt Extraction Test Suite")
    print("=" * 50)
    
    tests = [
        ("Workflow Parameter Extraction", test_workflow_parameter_extraction),
        ("Enhanced Prompt Formatting", test_enhanced_prompt_formatting),
        ("Image Info Enhanced Extraction", test_image_info_enhanced_extraction),
        ("Backward Compatibility", test_backward_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, success in results:
        status = "✓ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced prompt extraction is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
