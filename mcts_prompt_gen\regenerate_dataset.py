#!/usr/bin/env python3
"""
Dataset Regeneration Script with Improved LoRA Extraction

This script regenerates the prompt dataset using the new graph-based LoRA extraction
logic to ensure only connected LoRA nodes are included in the training data.

Usage:
    python regenerate_dataset.py [--input-file promptlabels.pkl] [--output-file promptlabels_v2.pkl]
"""

import argparse
import os
import sys
import dill
from datetime import datetime
from pathlib import Path

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import image_info
from PIL import Image


def regenerate_prompt_data(input_file, output_file, verbose=False):
    """
    Regenerate prompt data using improved LoRA extraction.
    
    Args:
        input_file: Path to existing promptlabels.pkl file
        output_file: Path to save regenerated data
        verbose: Whether to print detailed progress
    
    Returns:
        tuple: (success_count, error_count, total_count)
    """
    print(f"Loading existing dataset from: {input_file}")
    
    # Load existing data
    try:
        with open(input_file, 'rb') as f:
            existing_data = dill.load(f)
    except Exception as e:
        print(f"Error loading input file: {e}")
        return 0, 0, 0
    
    print(f"Loaded {len(existing_data)} entries from existing dataset")
    
    # Regenerate data with improved extraction
    regenerated_data = []
    success_count = 0
    error_count = 0
    total_count = len(existing_data)
    
    print("Regenerating prompts with improved LoRA extraction...")
    
    for i, (filename, old_prompt, goodness) in enumerate(existing_data):
        if verbose and i % 1000 == 0:
            print(f"Processing {i}/{total_count} ({i/total_count*100:.1f}%)")
        
        try:
            # Check if file still exists
            if not os.path.exists(filename):
                if verbose:
                    print(f"Warning: File not found: {filename}")
                # Keep old data if file is missing
                regenerated_data.append((filename, old_prompt, goodness))
                error_count += 1
                continue
            
            # Re-extract prompt with improved logic
            with Image.open(filename) as img:
                info = image_info(img, filename, enhanced_prompts=True)
                new_prompt = info.get('prompt', '')
            
            if new_prompt:
                regenerated_data.append((filename, new_prompt, goodness))
                success_count += 1
                
                # Log significant changes in LoRA extraction
                if verbose and '--lora' in new_prompt and '--lora' in old_prompt:
                    old_loras = [part for part in old_prompt.split() if part.startswith('--lora')]
                    new_loras = [part for part in new_prompt.split() if part.startswith('--lora')]
                    if len(old_loras) != len(new_loras):
                        print(f"LoRA change in {filename}: {len(old_loras)} -> {len(new_loras)} LoRAs")
            else:
                # Keep old prompt if extraction fails
                regenerated_data.append((filename, old_prompt, goodness))
                error_count += 1
                
        except Exception as e:
            if verbose:
                print(f"Error processing {filename}: {e}")
            # Keep old data on error
            regenerated_data.append((filename, old_prompt, goodness))
            error_count += 1
    
    # Save regenerated data
    print(f"Saving regenerated dataset to: {output_file}")
    try:
        with open(output_file, 'wb') as f:
            dill.dump(regenerated_data, f)
        print(f"Successfully saved {len(regenerated_data)} entries")
    except Exception as e:
        print(f"Error saving output file: {e}")
        return success_count, error_count, total_count
    
    return success_count, error_count, total_count


def analyze_changes(old_file, new_file):
    """
    Analyze the differences between old and new datasets.
    
    Args:
        old_file: Path to original dataset
        new_file: Path to regenerated dataset
    """
    print("\nAnalyzing changes between datasets...")
    
    try:
        with open(old_file, 'rb') as f:
            old_data = dill.load(f)
        with open(new_file, 'rb') as f:
            new_data = dill.load(f)
    except Exception as e:
        print(f"Error loading files for analysis: {e}")
        return
    
    # Create lookup dictionaries
    old_dict = {filename: prompt for filename, prompt, _ in old_data}
    new_dict = {filename: prompt for filename, prompt, _ in new_data}
    
    # Count changes
    lora_changes = 0
    lora_reductions = 0
    lora_increases = 0
    
    for filename in old_dict:
        if filename in new_dict:
            old_prompt = old_dict[filename]
            new_prompt = new_dict[filename]
            
            old_loras = len([part for part in old_prompt.split() if part.startswith('--lora')])
            new_loras = len([part for part in new_prompt.split() if part.startswith('--lora')])
            
            if old_loras != new_loras:
                lora_changes += 1
                if new_loras < old_loras:
                    lora_reductions += 1
                else:
                    lora_increases += 1
    
    print(f"LoRA extraction changes:")
    print(f"  Total files with LoRA changes: {lora_changes}")
    print(f"  Files with fewer LoRAs (disconnected removed): {lora_reductions}")
    print(f"  Files with more LoRAs (new connections found): {lora_increases}")
    print(f"  Percentage of files affected: {lora_changes/len(old_dict)*100:.2f}%")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Regenerate dataset with improved LoRA extraction")
    parser.add_argument('--input-file', default='promptlabels.pkl', 
                       help='Input dataset file (default: promptlabels.pkl)')
    parser.add_argument('--output-file', default='promptlabels_v2.pkl',
                       help='Output dataset file (default: promptlabels_v2.pkl)')
    parser.add_argument('--verbose', action='store_true',
                       help='Print detailed progress information')
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze changes between old and new datasets')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found")
        print("Please run the data cleaning script first to generate the dataset")
        return 1
    
    # Create backup of original file
    if args.input_file != args.output_file:
        backup_file = f"{args.input_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        print(f"Creating backup: {backup_file}")
        try:
            import shutil
            shutil.copy2(args.input_file, backup_file)
        except Exception as e:
            print(f"Warning: Could not create backup: {e}")
    
    print("=" * 60)
    print("Dataset Regeneration with Improved LoRA Extraction")
    print("=" * 60)
    print(f"Input file: {args.input_file}")
    print(f"Output file: {args.output_file}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Regenerate dataset
    success_count, error_count, total_count = regenerate_prompt_data(
        args.input_file, args.output_file, args.verbose
    )
    
    # Print summary
    print("\n" + "=" * 60)
    print("REGENERATION SUMMARY")
    print("=" * 60)
    print(f"Total entries processed: {total_count}")
    print(f"Successfully regenerated: {success_count}")
    print(f"Errors/kept original: {error_count}")
    print(f"Success rate: {success_count/total_count*100:.2f}%")
    
    # Analyze changes if requested
    if args.analyze and args.input_file != args.output_file:
        analyze_changes(args.input_file, args.output_file)
    
    print(f"\nRegenerated dataset saved to: {args.output_file}")
    print("You can now use this improved dataset for ML training.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
